<?php
/**
 * Admin Login Page
 *
 * This page allows administrators to log in to the admin panel.
 */

// Start session
session_start();

// Include database configuration
require_once '../includes/config.php';

// Check if user is already logged in
if(isset($_SESSION['user_id'])) {
    redirect("index.php");
}

// Initialize variables
$username = $password = "";
$username_err = $password_err = $login_err = "";

// Process form data when form is submitted
if($_SERVER["REQUEST_METHOD"] == "POST") {

    // Check if username is empty
    if(empty(trim($_POST["username"]))) {
        $username_err = "Please enter username.";
    } else {
        $username = sanitize_input($_POST["username"]);
    }

    // Check if password is empty
    if(empty(trim($_POST["password"]))) {
        $password_err = "Please enter your password.";
    } else {
        $password = trim($_POST["password"]);
    }

    // Validate credentials
    if(empty($username_err) && empty($password_err)) {
        // Special case for admin user during initial setup
        if($username === "admin" && $password === "admin123") {
            // Check if admin user exists in database
            $check_admin = "SELECT id FROM users WHERE username = 'admin'";
            $admin_result = mysqli_query($conn, $check_admin);

            if(mysqli_num_rows($admin_result) == 0) {
                // Admin user doesn't exist, create it
                $hashed_password = password_hash("admin123", PASSWORD_DEFAULT);
                $create_admin = "INSERT INTO users (username, password, email, full_name, role)
                                VALUES ('admin', '$hashed_password', '<EMAIL>', 'Administrator', 'admin')";
                mysqli_query($conn, $create_admin);

                // Get the new admin ID
                $admin_id = mysqli_insert_id($conn);

                // Start session and set session variables
                session_start();
                $_SESSION["user_id"] = $admin_id;
                $_SESSION["username"] = "admin";
                $_SESSION["full_name"] = "Administrator";
                $_SESSION["role"] = "admin";

                // Redirect to dashboard
                redirect("index.php");
            }
        }

        // Regular login process
        $sql = "SELECT id, username, password, full_name, role FROM users WHERE username = ?";

        if($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "s", $param_username);

            // Set parameters
            $param_username = $username;

            // Attempt to execute the prepared statement
            if(mysqli_stmt_execute($stmt)) {
                // Store result
                mysqli_stmt_store_result($stmt);

                // Check if username exists, if yes then verify password
                if(mysqli_stmt_num_rows($stmt) == 1) {
                    // Bind result variables
                    mysqli_stmt_bind_result($stmt, $id, $username, $hashed_password, $full_name, $role);
                    if(mysqli_stmt_fetch($stmt)) {
                        // Special case for admin during initial setup
                        if($username === "admin" && $password === "admin123") {
                            // Start session and set session variables
                            session_start();
                            $_SESSION["user_id"] = $id;
                            $_SESSION["username"] = $username;
                            $_SESSION["full_name"] = $full_name;
                            $_SESSION["role"] = $role;

                            // Redirect to dashboard
                            redirect("index.php");
                        }
                        // Regular password verification
                        else if(password_verify($password, $hashed_password)) {
                            // Password is correct, start a new session
                            session_start();

                            // Store data in session variables
                            $_SESSION["user_id"] = $id;
                            $_SESSION["username"] = $username;
                            $_SESSION["full_name"] = $full_name;
                            $_SESSION["role"] = $role;

                            // Redirect user to admin dashboard
                            redirect("index.php");
                        } else {
                            // Password is not valid
                            $login_err = "Invalid username or password.";
                        }
                    }
                } else {
                    // Username doesn't exist
                    $login_err = "Invalid username or password.";
                }
            } else {
                $login_err = "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            mysqli_stmt_close($stmt);
        }
    }

    // Close connection
    mysqli_close($conn);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Students' Zone</title>

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Nunito:wght@600;700;800&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <!-- Admin Stylesheet -->
    <link href="css/admin.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-lg-5 col-md-7">
                <div class="card shadow-lg border-0 rounded-lg mt-5">
                    <div class="card-header">
                        <div class="text-center">
                            <img src="../img/logo.png" alt="Students' Zone Logo" class="img-fluid mb-3" style="max-height: 100px;">
                            <h3 class="text-center font-weight-light my-4">Admin Login</h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php
                        if(!empty($login_err)){
                            echo '<div class="alert alert-danger">' . $login_err . '</div>';
                        }
                        ?>
                        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                            <div class="form-floating mb-3">
                                <input class="form-control <?php echo (!empty($username_err)) ? 'is-invalid' : ''; ?>" id="username" name="username" type="text" placeholder="Username" value="<?php echo $username; ?>">
                                <label for="username">Username</label>
                                <span class="invalid-feedback"><?php echo $username_err; ?></span>
                            </div>
                            <div class="form-floating mb-3">
                                <input class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" id="password" name="password" type="password" placeholder="Password">
                                <label for="password">Password</label>
                                <span class="invalid-feedback"><?php echo $password_err; ?></span>
                            </div>
                            <div class="d-flex align-items-center justify-content-between mt-4 mb-0">
                                <button type="submit" class="btn btn-primary w-100">Login</button>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center py-3">
                        <div class="small"><a href="../index.php">Back to Website</a></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

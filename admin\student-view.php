<?php
/**
 * View Student
 * 
 * This page displays the details of a specific student.
 */

// Include header
include 'includes/header.php';

// Check if ID is provided
if(!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Student ID is required.";
    redirect("students.php");
}

$id = sanitize_input($_GET['id']);

// Get student details
$sql = "SELECT s.* 
        FROM students s 
        WHERE s.id = '$id'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) == 0) {
    $_SESSION['error'] = "Student not found.";
    redirect("students.php");
}

$student = mysqli_fetch_assoc($result);

// Get admission details
$sql_admission = "SELECT a.* 
                 FROM admissions a 
                 WHERE a.student_id = '$id'";
$result_admission = mysqli_query($conn, $sql_admission);
$has_admission = mysqli_num_rows($result_admission) > 0;
$admission = $has_admission ? mysqli_fetch_assoc($result_admission) : null;

// Get subjects
$sql_subjects = "SELECT sub.name, sub.type 
                FROM student_subjects ss 
                JOIN subjects sub ON ss.subject_id = sub.id 
                WHERE ss.student_id = '$id'";
$result_subjects = mysqli_query($conn, $sql_subjects);
?>

<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">View Student</h1>
    <div>
        <a href="students.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Students
        </a>
        <a href="student-edit.php?id=<?php echo $id; ?>" class="btn btn-warning">
            <i class="fas fa-edit"></i> Edit
        </a>
        <a href="student-delete.php?id=<?php echo $id; ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this student?');">
            <i class="fas fa-trash"></i> Delete
        </a>
    </div>
</div>

<!-- Student Details -->
<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Student Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Student ID</th>
                        <td><?php echo $student['id']; ?></td>
                    </tr>
                    <tr>
                        <th>Full Name</th>
                        <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                    </tr>
                    <tr>
                        <th>Parent Name</th>
                        <td><?php echo $student['parent_name']; ?></td>
                    </tr>
                    <tr>
                        <th>Date of Birth</th>
                        <td><?php echo date('F d, Y', strtotime($student['dob'])); ?></td>
                    </tr>
                    <tr>
                        <th>Place of Birth</th>
                        <td><?php echo $student['birth_place']; ?></td>
                    </tr>
                    <tr>
                        <th>Region</th>
                        <td><?php echo $student['region']; ?></td>
                    </tr>
                    <tr>
                        <th>Father's Occupation</th>
                        <td><?php echo $student['father_occupation']; ?></td>
                    </tr>
                    <tr>
                        <th>Father's Cell</th>
                        <td><?php echo $student['father_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Mother's Cell</th>
                        <td><?php echo $student['mother_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Student's Cell</th>
                        <td><?php echo $student['student_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Home Cell</th>
                        <td><?php echo $student['home_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Parent's Email</th>
                        <td><?php echo $student['parent_email']; ?></td>
                    </tr>
                    <tr>
                        <th>Student's Email</th>
                        <td><?php echo $student['student_email']; ?></td>
                    </tr>
                    <tr>
                        <th>Relation</th>
                        <td><?php echo $student['relation']; ?></td>
                    </tr>
                    <tr>
                        <th>Present School/College</th>
                        <td><?php echo $student['present_school']; ?></td>
                    </tr>
                    <tr>
                        <th>Address</th>
                        <td><?php echo $student['address']; ?></td>
                    </tr>
                    <tr>
                        <th>Registration Date</th>
                        <td><?php echo date('F d, Y', strtotime($student['created_at'])); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <?php if($has_admission): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Admission Details</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Admission ID</th>
                        <td><?php echo $admission['id']; ?></td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <?php if($admission['status'] == 'Pending'): ?>
                                <span class="badge bg-warning text-dark">Pending</span>
                            <?php elseif($admission['status'] == 'Approved'): ?>
                                <span class="badge bg-success">Approved</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Rejected</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Admission For</th>
                        <td><?php echo $admission['admission_for']; ?></td>
                    </tr>
                    <tr>
                        <th>Admission Type</th>
                        <td><?php echo $admission['admission_type']; ?></td>
                    </tr>
                    <tr>
                        <th>Class</th>
                        <td><?php echo $admission['class']; ?></td>
                    </tr>
                    <tr>
                        <th>Previous Class</th>
                        <td><?php echo $admission['prev_class']; ?></td>
                    </tr>
                    <tr>
                        <th>Percentage</th>
                        <td><?php echo $admission['percentage']; ?></td>
                    </tr>
                    <tr>
                        <th>Last School/College</th>
                        <td><?php echo $admission['last_school']; ?></td>
                    </tr>
                    <tr>
                        <th>Application Date</th>
                        <td><?php echo date('F d, Y', strtotime($admission['created_at'])); ?></td>
                    </tr>
                </table>
                
                <?php if($admission['status'] == 'Pending'): ?>
                <div class="mt-3">
                    <a href="admission-approve.php?id=<?php echo $admission['id']; ?>" class="btn btn-success" onclick="return confirm('Are you sure you want to approve this admission?');">
                        <i class="fas fa-check"></i> Approve Admission
                    </a>
                    <a href="admission-reject.php?id=<?php echo $admission['id']; ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to reject this admission?');">
                        <i class="fas fa-times"></i> Reject Admission
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php else: ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Admission Status</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    This student has not applied for admission yet.
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Subjects</h5>
            </div>
            <div class="card-body">
                <?php if(mysqli_num_rows($result_subjects) > 0): ?>
                <h6>Compulsory Subjects</h6>
                <ul class="list-group mb-3">
                    <?php 
                    mysqli_data_seek($result_subjects, 0);
                    while($subject = mysqli_fetch_assoc($result_subjects)) {
                        if($subject['type'] == 'Compulsory') {
                            echo "<li class='list-group-item'>{$subject['name']}</li>";
                        }
                    }
                    ?>
                </ul>
                
                <h6>Optional Subjects</h6>
                <ul class="list-group">
                    <?php 
                    mysqli_data_seek($result_subjects, 0);
                    $has_optional = false;
                    while($subject = mysqli_fetch_assoc($result_subjects)) {
                        if($subject['type'] == 'Optional') {
                            echo "<li class='list-group-item'>{$subject['name']}</li>";
                            $has_optional = true;
                        }
                    }
                    if(!$has_optional) {
                        echo "<li class='list-group-item'>No optional subjects selected</li>";
                    }
                    ?>
                </ul>
                <?php else: ?>
                <div class="alert alert-info">
                    No subjects found for this student.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>

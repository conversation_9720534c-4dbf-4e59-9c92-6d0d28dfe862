/* Admin Panel Stylesheet */

:root {
    --primary: #0a3d91; /* Dark Blue */
    --light: #F0FBFC;
    --dark: #0a5c36; /* Dark Green */
    --sidebar-width: 250px;
    --topbar-height: 70px;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Heebo', sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--primary);
    color: white;
    z-index: 1000;
    transition: all 0.3s;
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.1);
    text-align: center;
}

.sidebar-header img {
    max-width: 150px;
}

.sidebar-menu {
    padding: 0;
    list-style: none;
}

.sidebar-menu li {
    margin: 0;
    padding: 0;
}

.sidebar-menu li a {
    display: block;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.sidebar-menu li a:hover,
.sidebar-menu li a.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: white;
}

.sidebar-menu li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Content Area Styles */
.content {
    margin-left: var(--sidebar-width);
    padding: 20px;
    min-height: calc(100vh - var(--topbar-height));
    transition: all 0.3s;
}

/* Topbar Styles */
.topbar {
    height: var(--topbar-height);
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.topbar .toggle-sidebar {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary);
}

.topbar .user-info {
    display: flex;
    align-items: center;
}

.topbar .user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.topbar .user-info .dropdown-toggle {
    color: var(--dark);
    text-decoration: none;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
}

.card-title {
    margin-bottom: 0;
    color: var(--primary);
}

/* Dashboard Stats */
.stat-card {
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
    background-color: var(--primary);
}

.stat-card.success {
    background-color: #28a745;
}

.stat-card.warning {
    background-color: #ffc107;
}

.stat-card.danger {
    background-color: #dc3545;
}

.stat-card .stat-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: bold;
}

.stat-card .stat-label {
    font-size: 1rem;
    opacity: 0.8;
}

/* Table Styles */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--primary);
    color: white;
    border: none;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(10, 61, 145, 0.25);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #082e6e;
    border-color: #082e6e;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .sidebar {
        margin-left: calc(var(--sidebar-width) * -1);
    }
    
    .sidebar.active {
        margin-left: 0;
    }
    
    .content {
        margin-left: 0;
    }
    
    .content.active {
        margin-left: var(--sidebar-width);
    }
}

/* Login Page Styles */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: #f8f9fa;
}

.login-card {
    max-width: 400px;
    width: 100%;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    background-color: white;
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo img {
    max-width: 150px;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    padding: 15px 20px;
}

/* Pagination Styles */
.pagination {
    margin-bottom: 0;
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: #082e6e;
}

<?php
/**
 * Process Admission Form
 * 
 * This script processes the admission form submission and stores the data in the database.
 */

// Start session
session_start();

// Include database configuration
require_once 'includes/config.php';

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Validate and sanitize input data
    // Admission details
    $admission_for = sanitize_input($_POST['admission_for']);
    $admission_type = sanitize_input($_POST['admission_type']);
    $class = sanitize_input($_POST['class']);
    $prev_class = sanitize_input($_POST['prev_class']);
    $percentage = sanitize_input($_POST['percentage']);
    $last_school = sanitize_input($_POST['last_school']);
    
    // Optional subjects
    $optional_subjects = isset($_POST['optional_subjects']) ? $_POST['optional_subjects'] : [];
    
    // Personal information
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $parent_name = sanitize_input($_POST['parent_name']);
    $birth_place = sanitize_input($_POST['birth_place']);
    $region = sanitize_input($_POST['region']);
    $father_occupation = sanitize_input($_POST['father_occupation']);
    $father_cell = sanitize_input($_POST['father_cell']);
    $mother_cell = sanitize_input($_POST['mother_cell']);
    $address = sanitize_input($_POST['address']);
    $student_cell = sanitize_input($_POST['student_cell']);
    $home_cell = sanitize_input($_POST['home_cell']);
    $present_school = sanitize_input($_POST['present_school']);
    $dob = sanitize_input($_POST['dob']);
    $parent_email = sanitize_input($_POST['parent_email']);
    $student_email = sanitize_input($_POST['student_email']);
    $relation = sanitize_input($_POST['relation']);
    
    // Check if student email already exists
    $check_email = "SELECT id FROM students WHERE student_email = '$student_email'";
    $result = mysqli_query($conn, $check_email);
    
    if (mysqli_num_rows($result) > 0) {
        // Student already exists
        $_SESSION['error'] = "A student with this email already exists.";
        redirect("admission.php");
    }
    
    // Begin transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Insert student data
        $sql_student = "INSERT INTO students (first_name, last_name, parent_name, birth_place, region, 
                        father_occupation, father_cell, mother_cell, address, student_cell, 
                        home_cell, present_school, dob, parent_email, student_email, relation) 
                        VALUES ('$first_name', '$last_name', '$parent_name', '$birth_place', '$region', 
                        '$father_occupation', '$father_cell', '$mother_cell', '$address', '$student_cell', 
                        '$home_cell', '$present_school', '$dob', '$parent_email', '$student_email', '$relation')";
        
        if (!mysqli_query($conn, $sql_student)) {
            throw new Exception("Error inserting student data: " . mysqli_error($conn));
        }
        
        // Get the student ID
        $student_id = mysqli_insert_id($conn);
        
        // Insert admission data
        $sql_admission = "INSERT INTO admissions (student_id, admission_for, admission_type, class, prev_class, percentage, last_school) 
                          VALUES ('$student_id', '$admission_for', '$admission_type', '$class', '$prev_class', '$percentage', '$last_school')";
        
        if (!mysqli_query($conn, $sql_admission)) {
            throw new Exception("Error inserting admission data: " . mysqli_error($conn));
        }
        
        // Insert compulsory subjects
        $compulsory_subjects = ['English', 'Urdu', 'Mathematics', 'Pakistan Studies', 'Islamic Studies'];
        
        foreach ($compulsory_subjects as $subject) {
            // Get subject ID
            $sql_subject = "SELECT id FROM subjects WHERE name = '$subject' AND type = 'Compulsory'";
            $result = mysqli_query($conn, $sql_subject);
            
            if (mysqli_num_rows($result) > 0) {
                $row = mysqli_fetch_assoc($result);
                $subject_id = $row['id'];
                
                // Insert into student_subjects
                $sql_student_subject = "INSERT INTO student_subjects (student_id, subject_id) VALUES ('$student_id', '$subject_id')";
                
                if (!mysqli_query($conn, $sql_student_subject)) {
                    throw new Exception("Error inserting compulsory subject: " . mysqli_error($conn));
                }
            }
        }
        
        // Insert optional subjects
        foreach ($optional_subjects as $subject) {
            $subject = sanitize_input($subject);
            
            // Get subject ID
            $sql_subject = "SELECT id FROM subjects WHERE name = '$subject' AND type = 'Optional'";
            $result = mysqli_query($conn, $sql_subject);
            
            if (mysqli_num_rows($result) > 0) {
                $row = mysqli_fetch_assoc($result);
                $subject_id = $row['id'];
                
                // Insert into student_subjects
                $sql_student_subject = "INSERT INTO student_subjects (student_id, subject_id) VALUES ('$student_id', '$subject_id')";
                
                if (!mysqli_query($conn, $sql_student_subject)) {
                    throw new Exception("Error inserting optional subject: " . mysqli_error($conn));
                }
            }
        }
        
        // Commit transaction
        mysqli_commit($conn);
        
        // Set success message
        $_SESSION['success'] = "Your admission application has been submitted successfully. We will contact you soon.";
        
        // Redirect to admission page
        redirect("admission.php");
        
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        
        // Set error message
        $_SESSION['error'] = $e->getMessage();
        
        // Redirect to admission page
        redirect("admission.php");
    }
    
} else {
    // If not submitted via POST, redirect to admission page
    redirect("admission.php");
}
?>

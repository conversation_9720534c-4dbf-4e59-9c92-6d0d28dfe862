<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup Instructions - Students' Zone</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #0a3d91;
        }
        .alert {
            margin: 20px 0;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>Students' Zone Admin Setup Instructions</h1>
    
    <div class="alert alert-info">
        <strong>Note:</strong> These instructions will help you set up the admin panel for the Students' Zone website.
    </div>
    
    <div class="step">
        <h2>Step 1: Database Setup</h2>
        <p>First, you need to create the database and tables:</p>
        <ol>
            <li>Open phpMyAdmin or your preferred MySQL management tool</li>
            <li>Create a new database named <code>studentszone</code></li>
            <li>Import the SQL file from <code>database/studentszone.sql</code></li>
        </ol>
    </div>
    
    <div class="step">
        <h2>Step 2: Reset Admin Password</h2>
        <p>To set up the admin password:</p>
        <ol>
            <li>Navigate to <a href="reset_admin_password.php">reset_admin_password.php</a></li>
            <li>This script will automatically:
                <ul>
                    <li>Create an admin user if it doesn't exist</li>
                    <li>Reset the admin password to <code>admin123</code></li>
                </ul>
            </li>
            <li>After running the script, you should see a success message</li>
        </ol>
    </div>
    
    <div class="step">
        <h2>Step 3: Login to Admin Panel</h2>
        <p>Now you can log in to the admin panel:</p>
        <ol>
            <li>Go to <a href="login.php">login.php</a></li>
            <li>Use the following credentials:
                <ul>
                    <li>Username: <code>admin</code></li>
                    <li>Password: <code>admin123</code></li>
                </ul>
            </li>
            <li>After logging in, you'll be redirected to the admin dashboard</li>
        </ol>
    </div>
    
    <div class="step">
        <h2>Step 4: Change Default Password</h2>
        <p>For security reasons, it's recommended to change the default password:</p>
        <ol>
            <li>After logging in, go to the Profile section</li>
            <li>Use the "Change Password" option to set a new, secure password</li>
        </ol>
    </div>
    
    <div class="alert alert-warning">
        <strong>Security Warning:</strong> Make sure to change the default admin password after the initial setup to protect your admin panel from unauthorized access.
    </div>
    
    <p><a href="login.php" class="btn btn-primary">Go to Admin Login</a></p>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

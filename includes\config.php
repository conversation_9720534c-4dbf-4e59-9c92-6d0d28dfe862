<?php
/**
 * Database Configuration File
 * 
 * This file contains the database connection settings for the Students' Zone website.
 */

// Database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'studentszone');

// Attempt to connect to MySQL database
$conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if($conn === false) {
    die("ERROR: Could not connect to database. " . mysqli_connect_error());
}

// Set charset to ensure proper encoding
mysqli_set_charset($conn, "utf8mb4");

/**
 * Helper function to sanitize input data
 * 
 * @param string $data Data to be sanitized
 * @return string Sanitized data
 */
function sanitize_input($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    $data = mysqli_real_escape_string($conn, $data);
    return $data;
}

/**
 * Helper function to redirect to a specific page
 * 
 * @param string $location URL to redirect to
 */
function redirect($location) {
    header("Location: $location");
    exit;
}

/**
 * Helper function to display error messages
 * 
 * @param string $message Error message to display
 */
function display_error($message) {
    return "<div class='alert alert-danger'>$message</div>";
}

/**
 * Helper function to display success messages
 * 
 * @param string $message Success message to display
 */
function display_success($message) {
    return "<div class='alert alert-success'>$message</div>";
}

/**
 * Helper function to check if user is logged in
 * 
 * @return boolean True if user is logged in, false otherwise
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Helper function to require login
 * Redirects to login page if user is not logged in
 */
function require_login() {
    if(!is_logged_in()) {
        redirect("login.php");
    }
}
?>

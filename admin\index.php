<?php
/**
 * Admin Dashboard
 * 
 * This is the main dashboard for the admin panel.
 */

// Include header
include 'includes/header.php';

// Get statistics
// Total students
$sql_students = "SELECT COUNT(*) as total FROM students";
$result_students = mysqli_query($conn, $sql_students);
$total_students = mysqli_fetch_assoc($result_students)['total'];

// Total admissions
$sql_admissions = "SELECT COUNT(*) as total FROM admissions";
$result_admissions = mysqli_query($conn, $sql_admissions);
$total_admissions = mysqli_fetch_assoc($result_admissions)['total'];

// Pending admissions
$sql_pending = "SELECT COUNT(*) as total FROM admissions WHERE status = 'Pending'";
$result_pending = mysqli_query($conn, $sql_pending);
$pending_admissions = mysqli_fetch_assoc($result_pending)['total'];

// Approved admissions
$sql_approved = "SELECT COUNT(*) as total FROM admissions WHERE status = 'Approved'";
$result_approved = mysqli_query($conn, $sql_approved);
$approved_admissions = mysqli_fetch_assoc($result_approved)['total'];

// Recent admissions
$sql_recent = "SELECT a.id, a.created_at, a.status, s.first_name, s.last_name, a.class, a.admission_for 
               FROM admissions a 
               JOIN students s ON a.student_id = s.id 
               ORDER BY a.created_at DESC 
               LIMIT 5";
$result_recent = mysqli_query($conn, $sql_recent);
?>

<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
</div>

<!-- Stats Cards -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="stat-card primary">
            <div class="stat-icon">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="stat-value"><?php echo $total_students; ?></div>
            <div class="stat-label">Total Students</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card success">
            <div class="stat-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-value"><?php echo $total_admissions; ?></div>
            <div class="stat-label">Total Admissions</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card warning">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value"><?php echo $pending_admissions; ?></div>
            <div class="stat-label">Pending Admissions</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stat-card danger">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-value"><?php echo $approved_admissions; ?></div>
            <div class="stat-label">Approved Admissions</div>
        </div>
    </div>
</div>

<!-- Recent Admissions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Recent Admissions</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Class</th>
                                <th>For</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(mysqli_num_rows($result_recent) > 0): ?>
                                <?php while($row = mysqli_fetch_assoc($result_recent)): ?>
                                    <tr>
                                        <td><?php echo $row['id']; ?></td>
                                        <td><?php echo $row['first_name'] . ' ' . $row['last_name']; ?></td>
                                        <td><?php echo $row['class']; ?></td>
                                        <td><?php echo $row['admission_for']; ?></td>
                                        <td>
                                            <?php if($row['status'] == 'Pending'): ?>
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            <?php elseif($row['status'] == 'Approved'): ?>
                                                <span class="badge bg-success">Approved</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Rejected</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('M d, Y', strtotime($row['created_at'])); ?></td>
                                        <td>
                                            <a href="admission-view.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No recent admissions found.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-end mt-3">
                    <a href="admissions.php" class="btn btn-primary">View All Admissions</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Links -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Quick Links</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="students.php" class="btn btn-primary w-100">
                            <i class="fas fa-user-graduate me-2"></i> Manage Students
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="admissions.php" class="btn btn-success w-100">
                            <i class="fas fa-file-alt me-2"></i> Manage Admissions
                        </a>
             
                </div>
            </div>
        </div>
    </div>
    <!-- <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">System Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tbody>
                        <tr>
                            <th>PHP Version</th>
                            <td><?php echo phpversion(); ?></td>
                        </tr>
                        <tr>
                            <th>MySQL Version</th>
                            <td><?php echo mysqli_get_server_info($conn); ?></td>
                        </tr>
                        <tr>
                            <th>Server Software</th>
                            <td><?php echo $_SERVER['SERVER_SOFTWARE']; ?></td>
                        </tr>
                        <tr>
                            <th>Current User</th>
                            <td><?php echo $_SESSION['full_name']; ?> (<?php echo $_SESSION['role']; ?>)</td>
                        </tr>
                        <tr>
                            <th>Date & Time</th>
                            <td><?php echo date('Y-m-d H:i:s'); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div> -->
</div>

<?php
// Include footer
include 'includes/footer.php';
?>

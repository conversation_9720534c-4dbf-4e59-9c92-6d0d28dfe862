-- Create database if not exists
CREATE DATABASE IF NOT EXISTS studentszone;

-- Use the database
USE studentszone;

-- Create users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','staff') NOT NULL DEFAULT 'staff',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default admin user (password: admin123)
INSERT INTO `users` (`username`, `password`, `email`, `full_name`, `role`) VALUES
('admin', 'admin123', '<EMAIL>', 'Administrator', 'admin');

-- Note: The password hash above is a placeholder. Use the reset_admin_password.php script to set the actual password.


-- Create students table
CREATE TABLE IF NOT EXISTS `students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `parent_name` varchar(100) NOT NULL,
  `birth_place` varchar(100) NOT NULL,
  `region` varchar(100) NOT NULL,
  `father_occupation` varchar(100) NOT NULL,
  `father_cell` varchar(20) NOT NULL,
  `mother_cell` varchar(20) NOT NULL,
  `address` text NOT NULL,
  `student_cell` varchar(20) NOT NULL,
  `home_cell` varchar(20) NOT NULL,
  `present_school` varchar(100) NOT NULL,
  `dob` date NOT NULL,
  `parent_email` varchar(100) NOT NULL,
  `student_email` varchar(100) NOT NULL,
  `relation` enum('Father','Mother','Guardian') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create admissions table
CREATE TABLE IF NOT EXISTS `admissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `admission_for` enum('School','College') NOT NULL,
  `admission_type` enum('New','Transfer') NOT NULL,
  `class` varchar(20) NOT NULL,
  `prev_class` varchar(20) NOT NULL,
  `percentage` varchar(10) NOT NULL,
  `last_school` varchar(100) NOT NULL,
  `status` enum('Pending','Approved','Rejected') NOT NULL DEFAULT 'Pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `admissions_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create subjects table
CREATE TABLE IF NOT EXISTS `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` enum('Compulsory','Optional') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default subjects
INSERT INTO `subjects` (`name`, `type`) VALUES
('English', 'Compulsory'),
('Urdu', 'Compulsory'),
('Mathematics', 'Compulsory'),
('Pakistan Studies', 'Compulsory'),
('Islamic Studies', 'Compulsory'),
('Physics', 'Optional'),
('Chemistry', 'Optional'),
('Biology', 'Optional'),
('Computer Science', 'Optional'),
('Economics', 'Optional');

-- Create student_subjects table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS `student_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_subject` (`student_id`,`subject_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `student_subjects_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create courses table
CREATE TABLE IF NOT EXISTS `courses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default courses
INSERT INTO `courses` (`name`, `description`, `image`) VALUES
('Pre-Medical', 'Prepare for medical school with our comprehensive pre-medical program.', 'img/pre-medical.jpeg'),
('Pre-Engineering', 'Build a strong foundation for engineering studies with our specialized program.', 'img/pre-engineering.jpeg'),
('ICS', 'Information and Computer Science program for technology enthusiasts.', 'img/ics.jpeg'),
('Commerce', 'Comprehensive commerce program for future business leaders.', 'img/commerce.jpeg');

<?php
/**
 * Reset Admin Password
 * 
 * This script resets the admin password to 'admin123'.
 */

// Include database configuration
require_once '../includes/config.php';

// Set the admin username and password
$username = 'admin';
$password = 'admin123';
$email = '<EMAIL>';
$full_name = 'Administrator';
$role = 'admin';

// Hash the password
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

// Check if admin user exists
$sql_check = "SELECT id FROM users WHERE username = '$username'";
$result = mysqli_query($conn, $sql_check);

if(mysqli_num_rows($result) > 0) {
    // Admin user exists, update password
    $sql = "UPDATE users SET password = '$hashed_password' WHERE username = '$username'";
    if(mysqli_query($conn, $sql)) {
        echo "Admin password has been reset successfully.<br>";
        echo "Username: $username<br>";
        echo "Password: $password<br>";
    } else {
        echo "Error updating password: " . mysqli_error($conn);
    }
} else {
    // Admin user doesn't exist, create new admin user
    $sql = "INSERT INTO users (username, password, email, full_name, role) VALUES ('$username', '$hashed_password', '$email', '$full_name', '$role')";
    if(mysqli_query($conn, $sql)) {
        echo "Admin user has been created successfully.<br>";
        echo "Username: $username<br>";
        echo "Password: $password<br>";
    } else {
        echo "Error creating admin user: " . mysqli_error($conn);
    }
}

// Close connection
mysqli_close($conn);
?>

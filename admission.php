<?php include 'includes/header.php'; ?>
<?php include 'includes/navbar.php'; ?>

<!-- Header Start -->
<div class="container-fluid bg-primary py-5 mb-5 page-header">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <h1 class="display-3 text-white animated slideInDown">Admission Form</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb justify-content-center">
                        <li class="breadcrumb-item"><a class="text-white" href="index.php">Home</a></li>
                        <li class="breadcrumb-item text-white active" aria-current="page">Admission</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<!-- Header End -->

<!-- Admission Form Start -->
<div class="container-xxl py-5">
    <div class="container">
        <div class="text-center wow fadeInUp" data-wow-delay="0.1s">
            <h6 class="section-title bg-white text-center text-primary px-3">Admission</h6>
            <h1 class="mb-5">ADMISSION FORM</h1>
        </div>
        <?php
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            // session_start();
        }

        // Display success message if set
        if(isset($_SESSION['success'])) {
            echo '<div class="alert alert-success">' . $_SESSION['success'] . '</div>';
            unset($_SESSION['success']);
        }

        // Display error message if set
        if(isset($_SESSION['error'])) {
            echo '<div class="alert alert-danger">' . $_SESSION['error'] . '</div>';
            unset($_SESSION['error']);
        }
        ?>
        <div class="row g-4">
            <div class="col-lg-12 col-md-12 wow fadeInUp" data-wow-delay="0.5s">
                <form action="process_admission.php" method="post">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <h5 class="mb-3">Admission Details</h5>
                            <div class="form-floating mb-3">
                                <select class="form-select" id="admission_for" name="admission_for" required>
                                    <option value="" selected disabled>Select</option>
                                    <option value="School">School</option>
                                    <option value="College">College</option>
                                </select>
                                <label for="admission_for">Admission for *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <select class="form-select" id="admission_type" name="admission_type" required>
                                    <option value="" selected disabled>Select</option>
                                    <option value="New">New</option>
                                    <option value="Transfer">Transfer</option>
                                </select>
                                <label for="admission_type">Admission type *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <select class="form-select" id="class" name="class" required>
                                    <option value="" selected disabled>Select</option>
                                    <option value="Class 1">Class 1</option>
                                    <option value="Class 2">Class 2</option>
                                    <option value="Class 3">Class 3</option>
                                    <option value="Class 4">Class 4</option>
                                    <option value="Class 5">Class 5</option>
                                    <option value="Class 6">Class 6</option>
                                    <option value="Class 7">Class 7</option>
                                    <option value="Class 8">Class 8</option>
                                    <option value="Class 9">Class 9</option>
                                    <option value="Class 10">Class 10</option>
                                    <option value="Class 11">Class 11</option>
                                    <option value="Class 12">Class 12</option>
                                </select>
                                <label for="class">Classes *</label>
                            </div>
                            <h5 class="mb-3 mt-4">Compulsory Subjects</h5>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="English" id="english" name="compulsory_subjects[]" checked disabled>
                                <label class="form-check-label" for="english">English</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Urdu" id="urdu" name="compulsory_subjects[]" checked disabled>
                                <label class="form-check-label" for="urdu">Urdu</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Mathematics" id="mathematics" name="compulsory_subjects[]" checked disabled>
                                <label class="form-check-label" for="mathematics">Mathematics</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Pakistan Studies" id="pakistan_studies" name="compulsory_subjects[]" checked disabled>
                                <label class="form-check-label" for="pakistan_studies">Pakistan Studies</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Islamic Studies" id="islamic_studies" name="compulsory_subjects[]" checked disabled>
                                <label class="form-check-label" for="islamic_studies">Islamic Studies</label>
                            </div>

                            <h5 class="mb-3 mt-4">Optional Subjects</h5>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Physics" id="physics" name="optional_subjects[]">
                                <label class="form-check-label" for="physics">Physics</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Chemistry" id="chemistry" name="optional_subjects[]">
                                <label class="form-check-label" for="chemistry">Chemistry</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Biology" id="biology" name="optional_subjects[]">
                                <label class="form-check-label" for="biology">Biology</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Computer Science" id="computer_science" name="optional_subjects[]">
                                <label class="form-check-label" for="computer_science">Computer Science</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="Economics" id="economics" name="optional_subjects[]">
                                <label class="form-check-label" for="economics">Economics</label>
                            </div>

                            <h5 class="mb-3 mt-4">Previous Examination Report</h5>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="prev_class" name="prev_class" placeholder="Class" required>
                                <label for="prev_class">Class *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="percentage" name="percentage" placeholder="Percentage %" required>
                                <label for="percentage">Percentage % *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="last_school" name="last_school" placeholder="Name of last School/College" required>
                                <label for="last_school">Name of last School/College *</label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h5 class="mb-3">Personal Information</h5>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="first_name" name="first_name" placeholder="First name" required>
                                <label for="first_name">First name *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Last name" required>
                                <label for="last_name">Last name *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="parent_name" name="parent_name" placeholder="S/o. D/o" required>
                                <label for="parent_name">S/o. D/o *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="birth_place" name="birth_place" placeholder="Place of Birth" required>
                                <label for="birth_place">Place of Birth *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="region" name="region" placeholder="Region" required>
                                <label for="region">Region *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="father_occupation" name="father_occupation" placeholder="Father's Occupation" required>
                                <label for="father_occupation">Father's Occupation *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="father_cell" name="father_cell" placeholder="Father's Cell#" required>
                                <label for="father_cell">Father's Cell# *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="mother_cell" name="mother_cell" placeholder="Mother's Cell#" required>
                                <label for="mother_cell">Mother's Cell# *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <textarea class="form-control" placeholder="Address" id="address" name="address" style="height: 100px" required></textarea>
                                <label for="address">Address *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="student_cell" name="student_cell" placeholder="Student's Cell" required>
                                <label for="student_cell">Student's Cell *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="home_cell" name="home_cell" placeholder="Home Cell" required>
                                <label for="home_cell">Home Cell *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="present_school" name="present_school" placeholder="Name Of Present School/College" required>
                                <label for="present_school">Name Of Present School/College *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="date" class="form-control" id="dob" name="dob" placeholder="Date Of Birth" required>
                                <label for="dob">Date Of Birth *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="parent_email" name="parent_email" placeholder="Parent's Email ID" required>
                                <label for="parent_email">Parent's Email ID *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="student_email" name="student_email" placeholder="Student's Email ID" required>
                                <label for="student_email">Student's Email ID *</label>
                            </div>
                            <div class="form-floating mb-3">
                                <select class="form-select" id="relation" name="relation" required>
                                    <option value="" selected disabled>Select</option>
                                    <option value="Father">Father</option>
                                    <option value="Mother">Mother</option>
                                    <option value="Guardian">Guardian</option>
                                </select>
                                <label for="relation">Relation *</label>
                            </div>
                        </div>

                        <div class="col-12">
                            <h5 class="mb-3">RULES & REGULATIONS</h5>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" value="1" id="agree_rules" name="agree_rules" required>
                                <label class="form-check-label" for="agree_rules">
                                    I have read and agree to the following rules and regulations:
                                </label>
                            </div>
                            <ul class="list-group mb-4">
                                <li class="list-group-item">Student should write correct name, address in the form and attach a copy of CNIC of their Parents / Guardians</li>
                                <li class="list-group-item">After the Admission, a card will be issued by the administration to the student and he/she is required to show the card for the entrance in the institute</li>
                                <li class="list-group-item">If any student is found smoking, using narcotics or involved in unwarranted behaviour, his/her name will be struck off from the rolls and the fee will not be refunded</li>
                                <li class="list-group-item">Students will be responsible for their vehicles, calculators, mobile and other things</li>
                                <li class="list-group-item">If the Students' card is lost, he/she will have to pay Rs. 200/- for the issuance of new card</li>
                                <li class="list-group-item">If due to some unavoidable reasons, any student is absent for a long period. His/Her application for the exemption of that month fee can be considered</li>
                                <li class="list-group-item">In Case of transfer from one section to the other, student will have to pay complete fees in the new section</li>
                                <li class="list-group-item">Institute reserves the right of refusal/cancellation of admission without any prior notice</li>
                                <li class="list-group-item">Installment fee must be paid with in start of every month or as per scheduled after due date Rs.500/- will be charged as late fee</li>
                                <li class="list-group-item">Student should mark their attendance at the time of reporting</li>
                                <li class="list-group-item">If any unseen situation occurs outside the Institute Students' Zone will not be responsible for it</li>
                                <li class="list-group-item">Name of the student will be registered after submission of the admission from along with any admission fee and first tuition fee</li>
                                <li class="list-group-item">Fees will not be refunded or transferred in any circumstances</li>
                                <li class="list-group-item">Mobile and any Entertainment devices are strictly not allowed</li>
                                <li class="list-group-item">Annual Prize Distribution ceremony charges are mandatory</li>
                            </ul>
                        </div>

                        <div class="col-12">
                            <button class="btn btn-primary w-100 py-3" type="submit">Submit Application</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Admission Form End -->

<!-- Features Start -->
<div class="container-xxl py-5">
    <div class="container">
        <div class="text-center wow fadeInUp" data-wow-delay="0.1s">
            <h6 class="section-title bg-white text-center text-primary px-3">Features</h6>
            <h1 class="mb-5">SALIENT FEATURES</h1>
        </div>
        <div class="row g-4">
            <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.1s">
                <div class="service-item text-center pt-3">
                    <div class="p-4">
                        <i class="fa fa-3x fa-users text-primary mb-4"></i>
                        <h5 class="mb-3">Parents Teachers Meeting</h5>
                        <p>Parents Teachers Meeting On Regular Basis</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.3s">
                <div class="service-item text-center pt-3">
                    <div class="p-4">
                        <i class="fa fa-3x fa-venus-mars text-primary mb-4"></i>
                        <h5 class="mb-3">Separate Classes</h5>
                        <p>Girls & Boys Separate classes</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.5s">
                <div class="service-item text-center pt-3">
                    <div class="p-4">
                        <i class="fa fa-3x fa-graduation-cap text-primary mb-4"></i>
                        <h5 class="mb-3">Supporting Classes</h5>
                        <p>Extra support for students who need it</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-sm-6 wow fadeInUp" data-wow-delay="0.7s">
                <div class="service-item text-center pt-3">
                    <div class="p-4">
                        <i class="fa fa-3x fa-book text-primary mb-4"></i>
                        <h5 class="mb-3">Study Notes</h5>
                        <p>Provides notes for all subjects according to new syllabus (available in Urdu & English)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Features End -->

<?php include 'includes/footer.php'; ?>

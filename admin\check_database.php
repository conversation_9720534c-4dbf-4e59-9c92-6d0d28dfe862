<?php
/**
 * Database Check
 * 
 * This script checks the database connection and tables.
 */

// Include database configuration
require_once '../includes/config.php';

echo "<h1>Database Connection Check</h1>";

// Check connection
if($conn) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
    exit;
}

echo "<h2>Database Information</h2>";
echo "<p>Server: " . mysqli_get_host_info($conn) . "</p>";
echo "<p>Server Version: " . mysqli_get_server_info($conn) . "</p>";

// Check if tables exist
echo "<h2>Table Check</h2>";
$tables = ['users', 'students', 'admissions', 'subjects', 'student_subjects', 'courses'];
$all_tables_exist = true;

echo "<ul>";
foreach($tables as $table) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
    $exists = mysqli_num_rows($result) > 0;
    
    if($exists) {
        echo "<li style='color: green;'>✓ Table '$table' exists</li>";
        
        // Count records
        $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM $table");
        $count = mysqli_fetch_assoc($count_result)['count'];
        echo " ($count records)";
    } else {
        echo "<li style='color: red;'>✗ Table '$table' does not exist</li>";
        $all_tables_exist = false;
    }
}
echo "</ul>";

// Check users table specifically
if(mysqli_query($conn, "SHOW TABLES LIKE 'users'")) {
    echo "<h2>Admin User Check</h2>";
    $result = mysqli_query($conn, "SELECT * FROM users WHERE username = 'admin'");
    
    if(mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        echo "<p style='color: green;'>✓ Admin user exists</p>";
        echo "<ul>";
        echo "<li>Username: " . $user['username'] . "</li>";
        echo "<li>Email: " . $user['email'] . "</li>";
        echo "<li>Full Name: " . $user['full_name'] . "</li>";
        echo "<li>Role: " . $user['role'] . "</li>";
        echo "</ul>";
        
        // Check if password is correct
        if(password_verify('admin123', $user['password'])) {
            echo "<p style='color: green;'>✓ Admin password is correctly set to 'admin123'</p>";
        } else {
            echo "<p style='color: red;'>✗ Admin password is not set to 'admin123'</p>";
            echo "<p>You can reset the admin password by visiting <a href='direct_login.php'>direct_login.php</a></p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Admin user does not exist</p>";
        echo "<p>You can create the admin user by visiting <a href='direct_login.php'>direct_login.php</a></p>";
    }
}

// Provide next steps
echo "<h2>Next Steps</h2>";
if($all_tables_exist) {
    echo "<p>All required tables exist. You can now:</p>";
    echo "<ul>";
    echo "<li><a href='direct_login.php'>Log in directly as admin</a></li>";
    echo "<li><a href='login.php'>Go to the login page</a></li>";
    echo "</ul>";
} else {
    echo "<p>Some tables are missing. You need to:</p>";
    echo "<ul>";
    echo "<li>Import the database schema from <code>database/studentszone.sql</code></li>";
    echo "<li>Then refresh this page to check again</li>";
    echo "</ul>";
}

// Close connection
mysqli_close($conn);
?>

<?php
/**
 * Delete Student
 * 
 * This script deletes a student and all associated records.
 */

// Include database configuration
require_once '../includes/config.php';

// Start session
session_start();

// Check if user is logged in
require_login();

// Check if ID is provided
if(!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Student ID is required.";
    redirect("students.php");
}

$id = sanitize_input($_GET['id']);

// Check if student exists
$sql = "SELECT * FROM students WHERE id = '$id'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) == 0) {
    $_SESSION['error'] = "Student not found.";
    redirect("students.php");
}

// Begin transaction
mysqli_begin_transaction($conn);

try {
    // Delete student subjects
    $sql_subjects = "DELETE FROM student_subjects WHERE student_id = '$id'";
    if(!mysqli_query($conn, $sql_subjects)) {
        throw new Exception("Error deleting student subjects: " . mysqli_error($conn));
    }
    
    // Delete admissions
    $sql_admissions = "DELETE FROM admissions WHERE student_id = '$id'";
    if(!mysqli_query($conn, $sql_admissions)) {
        throw new Exception("Error deleting student admissions: " . mysqli_error($conn));
    }
    
    // Delete student
    $sql_student = "DELETE FROM students WHERE id = '$id'";
    if(!mysqli_query($conn, $sql_student)) {
        throw new Exception("Error deleting student: " . mysqli_error($conn));
    }
    
    // Commit transaction
    mysqli_commit($conn);
    
    $_SESSION['success'] = "Student has been deleted successfully.";
    
} catch (Exception $e) {
    // Rollback transaction on error
    mysqli_rollback($conn);
    
    $_SESSION['error'] = $e->getMessage();
}

// Redirect back to students page
redirect("students.php");
?>

-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 27, 2025 at 07:25 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `studentszone`
--

-- --------------------------------------------------------

--
-- Table structure for table `admissions`
--

CREATE TABLE `admissions` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `admission_for` enum('School','College') NOT NULL,
  `admission_type` enum('New','Transfer') NOT NULL,
  `class` varchar(20) NOT NULL,
  `prev_class` varchar(20) NOT NULL,
  `percentage` varchar(10) NOT NULL,
  `last_school` varchar(100) NOT NULL,
  `status` enum('Pending','Approved','Rejected') NOT NULL DEFAULT 'Pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admissions`
--

INSERT INTO `admissions` (`id`, `student_id`, `admission_for`, `admission_type`, `class`, `prev_class`, `percentage`, `last_school`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'School', 'Transfer', 'Class 11', '10', '90', 'iqra', 'Approved', '2025-05-20 12:35:45', '2025-05-20 14:19:25');

-- --------------------------------------------------------

--
-- Table structure for table `courses`
--

CREATE TABLE `courses` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `courses`
--

INSERT INTO `courses` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES
(1, 'Pre-Medical', 'Prepare for medical school with our comprehensive pre-medical program.', 'img/pre-medical.jpeg', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(2, 'Pre-Engineering', 'Build a strong foundation for engineering studies with our specialized program.', 'img/pre-engineering.jpeg', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(3, 'ICS', 'Information and Computer Science program for technology enthusiasts.', 'img/ics.jpeg', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(4, 'Commerce', 'Comprehensive commerce program for future business leaders.', 'img/commerce.jpeg', '2025-05-20 11:52:52', '2025-05-20 11:52:52');

-- --------------------------------------------------------

--
-- Table structure for table `students`
--

CREATE TABLE `students` (
  `id` int(11) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `parent_name` varchar(100) NOT NULL,
  `birth_place` varchar(100) NOT NULL,
  `region` varchar(100) NOT NULL,
  `father_occupation` varchar(100) NOT NULL,
  `father_cell` varchar(20) NOT NULL,
  `mother_cell` varchar(20) NOT NULL,
  `address` text NOT NULL,
  `student_cell` varchar(20) NOT NULL,
  `home_cell` varchar(20) NOT NULL,
  `present_school` varchar(100) NOT NULL,
  `dob` date NOT NULL,
  `parent_email` varchar(100) NOT NULL,
  `student_email` varchar(100) NOT NULL,
  `relation` enum('Father','Mother','Guardian') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `students`
--

INSERT INTO `students` (`id`, `first_name`, `last_name`, `parent_name`, `birth_place`, `region`, `father_occupation`, `father_cell`, `mother_cell`, `address`, `student_cell`, `home_cell`, `present_school`, `dob`, `parent_email`, `student_email`, `relation`, `created_at`, `updated_at`) VALUES
(1, 'ahmed', 'iqra', 'asdfg', '234567', 'islamasdfg', 'zxdcfgh', '23456789', '2345678', 'block 1', '2345678', '23456789', 'iqra', '0005-12-31', '<EMAIL>', '<EMAIL>', 'Father', '2025-05-20 12:35:45', '2025-05-20 12:35:45');

-- --------------------------------------------------------

--
-- Table structure for table `student_subjects`
--

CREATE TABLE `student_subjects` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `student_subjects`
--

INSERT INTO `student_subjects` (`id`, `student_id`, `subject_id`, `created_at`) VALUES
(1, 1, 1, '2025-05-20 12:35:45'),
(2, 1, 2, '2025-05-20 12:35:45'),
(3, 1, 3, '2025-05-20 12:35:45'),
(4, 1, 4, '2025-05-20 12:35:45'),
(5, 1, 5, '2025-05-20 12:35:45'),
(6, 1, 6, '2025-05-20 12:35:45'),
(7, 1, 8, '2025-05-20 12:35:45');

-- --------------------------------------------------------

--
-- Table structure for table `subjects`
--

CREATE TABLE `subjects` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` enum('Compulsory','Optional') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `subjects`
--

INSERT INTO `subjects` (`id`, `name`, `type`, `created_at`, `updated_at`) VALUES
(1, 'English', 'Compulsory', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(2, 'Urdu', 'Compulsory', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(3, 'Mathematics', 'Compulsory', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(4, 'Pakistan Studies', 'Compulsory', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(5, 'Islamic Studies', 'Compulsory', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(6, 'Physics', 'Optional', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(7, 'Chemistry', 'Optional', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(8, 'Biology', 'Optional', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(9, 'Computer Science', 'Optional', '2025-05-20 11:52:52', '2025-05-20 11:52:52'),
(10, 'Economics', 'Optional', '2025-05-20 11:52:52', '2025-05-20 11:52:52');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','staff') NOT NULL DEFAULT 'staff',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `email`, `full_name`, `role`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$8Ux8OXMvZFDB1nL.UQsyVeQQMyOzxqE.i/GE9.kIUcDZgGzOKz4Hy', '<EMAIL>', 'Administrator', 'admin', '2025-05-20 11:52:51', '2025-05-20 11:52:51'),
(2, 'admin11', 'admin22', '<EMAIL>', 'admin admina', 'staff', '2025-05-20 12:28:49', '2025-05-20 12:28:49');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admissions`
--
ALTER TABLE `admissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `student_id` (`student_id`);

--
-- Indexes for table `courses`
--
ALTER TABLE `courses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `student_subjects`
--
ALTER TABLE `student_subjects`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `student_subject` (`student_id`,`subject_id`),
  ADD KEY `subject_id` (`subject_id`);

--
-- Indexes for table `subjects`
--
ALTER TABLE `subjects`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admissions`
--
ALTER TABLE `admissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `courses`
--
ALTER TABLE `courses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `students`
--
ALTER TABLE `students`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `student_subjects`
--
ALTER TABLE `student_subjects`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `subjects`
--
ALTER TABLE `subjects`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admissions`
--
ALTER TABLE `admissions`
  ADD CONSTRAINT `admissions_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_subjects`
--
ALTER TABLE `student_subjects`
  ADD CONSTRAINT `student_subjects_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

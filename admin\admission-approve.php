<?php
/**
 * Approve Admission
 * 
 * This script approves a pending admission.
 */

// Include database configuration
require_once '../includes/config.php';

// Start session
session_start();

// Check if user is logged in
require_login();

// Check if ID is provided
if(!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Admission ID is required.";
    redirect("admissions.php");
}

$id = sanitize_input($_GET['id']);

// Check if admission exists and is pending
$sql = "SELECT * FROM admissions WHERE id = '$id'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) == 0) {
    $_SESSION['error'] = "Admission not found.";
    redirect("admissions.php");
}

$admission = mysqli_fetch_assoc($result);

if($admission['status'] != 'Pending') {
    $_SESSION['error'] = "Only pending admissions can be approved.";
    redirect("admissions.php");
}

// Update admission status
$sql = "UPDATE admissions SET status = 'Approved', updated_at = NOW() WHERE id = '$id'";

if(mysqli_query($conn, $sql)) {
    $_SESSION['success'] = "Admission has been approved successfully.";
} else {
    $_SESSION['error'] = "Error approving admission: " . mysqli_error($conn);
}

// Redirect back to admissions page
redirect("admissions.php");
?>

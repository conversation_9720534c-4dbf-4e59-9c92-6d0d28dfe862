<?php
/**
 * View Admission
 * 
 * This page displays the details of a specific admission.
 */

// Include header
include 'includes/header.php';

// Check if ID is provided
if(!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "Admission ID is required.";
    redirect("admissions.php");
}

$id = sanitize_input($_GET['id']);

// Get admission details
$sql = "SELECT a.*, s.* 
        FROM admissions a 
        JOIN students s ON a.student_id = s.id 
        WHERE a.id = '$id'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) == 0) {
    $_SESSION['error'] = "Admission not found.";
    redirect("admissions.php");
}

$admission = mysqli_fetch_assoc($result);

// Get subjects
$sql_subjects = "SELECT sub.name, sub.type 
                FROM student_subjects ss 
                JOIN subjects sub ON ss.subject_id = sub.id 
                WHERE ss.student_id = '{$admission['student_id']}'";
$result_subjects = mysqli_query($conn, $sql_subjects);
?>

<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">View Admission</h1>
    <div>
        <a href="admissions.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Admissions
        </a>
        <?php if($admission['status'] == 'Pending'): ?>
            <a href="admission-approve.php?id=<?php echo $id; ?>" class="btn btn-success" onclick="return confirm('Are you sure you want to approve this admission?');">
                <i class="fas fa-check"></i> Approve
            </a>
            <a href="admission-reject.php?id=<?php echo $id; ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to reject this admission?');">
                <i class="fas fa-times"></i> Reject
            </a>
        <?php endif; ?>
    </div>
</div>

<!-- Admission Details -->
<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Admission Details</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Admission ID</th>
                        <td><?php echo $admission['id']; ?></td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <?php if($admission['status'] == 'Pending'): ?>
                                <span class="badge bg-warning text-dark">Pending</span>
                            <?php elseif($admission['status'] == 'Approved'): ?>
                                <span class="badge bg-success">Approved</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Rejected</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Admission For</th>
                        <td><?php echo $admission['admission_for']; ?></td>
                    </tr>
                    <tr>
                        <th>Admission Type</th>
                        <td><?php echo $admission['admission_type']; ?></td>
                    </tr>
                    <tr>
                        <th>Class</th>
                        <td><?php echo $admission['class']; ?></td>
                    </tr>
                    <tr>
                        <th>Previous Class</th>
                        <td><?php echo $admission['prev_class']; ?></td>
                    </tr>
                    <tr>
                        <th>Percentage</th>
                        <td><?php echo $admission['percentage']; ?></td>
                    </tr>
                    <tr>
                        <th>Last School/College</th>
                        <td><?php echo $admission['last_school']; ?></td>
                    </tr>
                    <tr>
                        <th>Application Date</th>
                        <td><?php echo date('F d, Y', strtotime($admission['created_at'])); ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Subjects</h5>
            </div>
            <div class="card-body">
                <h6>Compulsory Subjects</h6>
                <ul class="list-group mb-3">
                    <?php 
                    mysqli_data_seek($result_subjects, 0);
                    while($subject = mysqli_fetch_assoc($result_subjects)) {
                        if($subject['type'] == 'Compulsory') {
                            echo "<li class='list-group-item'>{$subject['name']}</li>";
                        }
                    }
                    ?>
                </ul>
                
                <h6>Optional Subjects</h6>
                <ul class="list-group">
                    <?php 
                    mysqli_data_seek($result_subjects, 0);
                    $has_optional = false;
                    while($subject = mysqli_fetch_assoc($result_subjects)) {
                        if($subject['type'] == 'Optional') {
                            echo "<li class='list-group-item'>{$subject['name']}</li>";
                            $has_optional = true;
                        }
                    }
                    if(!$has_optional) {
                        echo "<li class='list-group-item'>No optional subjects selected</li>";
                    }
                    ?>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Student Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Student ID</th>
                        <td><?php echo $admission['student_id']; ?></td>
                    </tr>
                    <tr>
                        <th>Full Name</th>
                        <td><?php echo $admission['first_name'] . ' ' . $admission['last_name']; ?></td>
                    </tr>
                    <tr>
                        <th>Parent Name</th>
                        <td><?php echo $admission['parent_name']; ?></td>
                    </tr>
                    <tr>
                        <th>Date of Birth</th>
                        <td><?php echo date('F d, Y', strtotime($admission['dob'])); ?></td>
                    </tr>
                    <tr>
                        <th>Place of Birth</th>
                        <td><?php echo $admission['birth_place']; ?></td>
                    </tr>
                    <tr>
                        <th>Region</th>
                        <td><?php echo $admission['region']; ?></td>
                    </tr>
                    <tr>
                        <th>Father's Occupation</th>
                        <td><?php echo $admission['father_occupation']; ?></td>
                    </tr>
                    <tr>
                        <th>Father's Cell</th>
                        <td><?php echo $admission['father_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Mother's Cell</th>
                        <td><?php echo $admission['mother_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Student's Cell</th>
                        <td><?php echo $admission['student_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Home Cell</th>
                        <td><?php echo $admission['home_cell']; ?></td>
                    </tr>
                    <tr>
                        <th>Parent's Email</th>
                        <td><?php echo $admission['parent_email']; ?></td>
                    </tr>
                    <tr>
                        <th>Student's Email</th>
                        <td><?php echo $admission['student_email']; ?></td>
                    </tr>
                    <tr>
                        <th>Relation</th>
                        <td><?php echo $admission['relation']; ?></td>
                    </tr>
                    <tr>
                        <th>Present School/College</th>
                        <td><?php echo $admission['present_school']; ?></td>
                    </tr>
                    <tr>
                        <th>Address</th>
                        <td><?php echo $admission['address']; ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>

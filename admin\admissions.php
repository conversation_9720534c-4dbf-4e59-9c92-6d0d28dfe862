<?php
/**
 * Admissions Management
 * 
 * This page displays a list of all admissions and allows administrators to manage them.
 */

// Include header
include 'includes/header.php';

// Get all admissions
$sql = "SELECT a.*, s.first_name, s.last_name, s.student_email, s.student_cell 
        FROM admissions a 
        JOIN students s ON a.student_id = s.id 
        ORDER BY a.id DESC";
$result = mysqli_query($conn, $sql);
?>

<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Admissions Management</h1>
</div>

<!-- Admissions List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">All Admissions</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Student Name</th>
                        <th>Email</th>
                        <th>Class</th>
                        <th>For</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(mysqli_num_rows($result) > 0): ?>
                        <?php while($row = mysqli_fetch_assoc($result)): ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><?php echo $row['first_name'] . ' ' . $row['last_name']; ?></td>
                                <td><?php echo $row['student_email']; ?></td>
                                <td><?php echo $row['class']; ?></td>
                                <td><?php echo $row['admission_for']; ?></td>
                                <td><?php echo $row['admission_type']; ?></td>
                                <td>
                                    <?php if($row['status'] == 'Pending'): ?>
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    <?php elseif($row['status'] == 'Approved'): ?>
                                        <span class="badge bg-success">Approved</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Rejected</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('M d, Y', strtotime($row['created_at'])); ?></td>
                                <td>
                                    <a href="admission-view.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if($row['status'] == 'Pending'): ?>
                                        <a href="admission-approve.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to approve this admission?');">
                                            <i class="fas fa-check"></i>
                                        </a>
                                        <a href="admission-reject.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this admission?');">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="text-center">No admissions found.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>

<?php
/**
 * Direct Admin Login
 * 
 * This script creates an admin user if it doesn't exist and logs in automatically.
 */

// Include database configuration
require_once '../includes/config.php';

// Start session
session_start();

// Set admin credentials
$username = 'admin';
$password = 'admin123';
$email = '<EMAIL>';
$full_name = 'Administrator';
$role = 'admin';

// Check if admin user exists
$sql = "SELECT id, username, full_name, role FROM users WHERE username = '$username'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    // Admin exists, get details
    $row = mysqli_fetch_assoc($result);
    $id = $row['id'];
    $full_name = $row['full_name'];
    $role = $row['role'];
    
    // Update password to ensure it works
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    $update_sql = "UPDATE users SET password = '$hashed_password' WHERE id = $id";
    mysqli_query($conn, $update_sql);
    
    echo "Admin user found. Password updated to 'admin123'.<br>";
} else {
    // Admin doesn't exist, create it
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, password, email, full_name, role) 
            VALUES ('$username', '$hashed_password', '$email', '$full_name', '$role')";
    
    if(mysqli_query($conn, $sql)) {
        $id = mysqli_insert_id($conn);
        echo "Admin user created successfully.<br>";
    } else {
        die("Error creating admin user: " . mysqli_error($conn));
    }
}

// Set session variables
$_SESSION["user_id"] = $id;
$_SESSION["username"] = $username;
$_SESSION["full_name"] = $full_name;
$_SESSION["role"] = $role;

echo "You are now logged in as admin.<br>";
echo "Redirecting to dashboard in 3 seconds...";
?>
<script>
    setTimeout(function() {
        window.location.href = "index.php";
    }, 3000);
</script>
